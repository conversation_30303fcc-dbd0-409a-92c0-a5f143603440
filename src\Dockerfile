# Stage 1: Build dependencies
FROM python:3.11-slim-bookworm as builder

ENV PYTHONDONTWRITEBYTECODE 1
ENV PYTHONUNBUFFERED 1

WORKDIR /app

RUN apt-get update && \
    apt-get install -y --no-install-recommends gcc libpq-dev && \
    apt-get clean && \
    rm -rf /var/lib/apt/lists/*

COPY src/requirements/requirements.txt .
RUN pip wheel --no-cache-dir --no-deps --wheel-dir /app/wheels -r requirements.txt

# Stage 2: Run application
FROM python:3.11-slim-bookworm

RUN useradd -m ceu-user

ENV HOME=/home/<USER>
ENV APP_HOME=/home/<USER>/app
ENV PYTHONDONTWRITEBYTECODE 1
ENV PYTHONUNBUFFERED 1
ENV PYTHONPATH=${APP_HOME}

WORKDIR $APP_HOME

RUN apt-get update && \
    apt-get install -y --no-install-recommends \
    gcc \
    libpq-dev \
    python3-cffi \
    python3-brotli \
    libpango-1.0-0 \
    libharfbuzz0b \
    libpangoft2-1.0-0 \
    libffi-dev \
    libjpeg-dev \
    libopenjp2-7-dev \
    && apt-get clean \
    && rm -rf /var/lib/apt/lists/*

COPY --from=builder /app/wheels /wheels
COPY --from=builder /app/requirements.txt .
RUN pip install --no-cache /wheels/*
RUN pip install whitenoise

COPY src/ $APP_HOME
COPY src/entrypoint.sh /entrypoint.sh

RUN chmod +x /entrypoint.sh

RUN mkdir -p $APP_HOME/staticfiles $APP_HOME/static && \
    chown -R ceu-user:ceu-user $APP_HOME && \
    chmod -R 755 $APP_HOME

USER ceu-user

# Debugging: List contents of APP_HOME
RUN echo "Contents of $APP_HOME:" && ls -R $APP_HOME

ENTRYPOINT ["/entrypoint.sh"]